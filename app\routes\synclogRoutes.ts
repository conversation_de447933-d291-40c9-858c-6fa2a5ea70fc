import express from 'express';
import { IQuery, RequestExtended } from '../interfaces/global';
import { synclogService } from '../services/synclogService';
import asyncHandler from '../utils/async-handler';
import {
	syncHubSpotInvoicesToXeroAsync,
	getAsyncOperationStatus,
} from '../services/syncHubspotXero';

const router = express.Router();

router.get(
	'/',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getSynclogs(req.query as IQuery);
	})
);

router.get(
	'/:id',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getSyncLogHistoryById(req.params.id);
	})
);

router.get(
	'/cogs',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getCOGSSynclogs(req.query as IQuery);
	})
);

router.get(
	'/b2c/synclogs',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getB2CSynclogs(req.query as IQuery);
	})
);

router.get(
	'/b2c/:id',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getB2CSyncLogHistoryById(req.params.id);
	})
);

router.get(
	'/shopify/orders',
	asyncHandler(async (req: RequestExtended) => {
		return synclogService.getOrdersByPaymentGatewayAndDate(req.query as IQuery);
	})
);

router.get(
	'/invoices/hubspot-xero',
	asyncHandler(async (req: RequestExtended) => {
		return syncHubSpotInvoicesToXeroAsync(
			req.query as {
				fromDate?: string;
				toDate?: string;
				page?: number;
			}
		);
	})
);

router.get(
	'/invoices/hubspot-xero/status/:operationId',
	asyncHandler(async (req: RequestExtended) => {
		const { operationId } = req.params;
		const status = getAsyncOperationStatus(operationId);

		if (!status) {
			return {
				error: 'Operation not found',
				message: 'The specified operation ID was not found or has expired',
			};
		}

		return status;
	})
);

export default router;
